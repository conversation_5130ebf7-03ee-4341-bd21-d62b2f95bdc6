import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  HeartHandshake, 
  ClipboardList, 
  Video, 
  Pill, 
  Users 
} from "lucide-react";

interface ConditionManagementData {
  condition: string;
  features: {
    nurseLineTitle: string;
    nurseLineDescription: string;
    symptomLoggerTitle: string;
    symptomLoggerDescription: string;
    telehealthTitle: string;
    telehealthDescription: string;
    medicationTitle: string;
    medicationDescription: string;
    careTeamTitle: string;
    careTeamDescription: string;
  };
}

const defaultConditionData: ConditionManagementData = {
  condition: "<PERSON><PERSON>hn's",
  features: {
    nurseLineTitle: "Dedicated Nurse Line",
    nurseLineDescription: "You'll be paired with a personal Coordinator Nurse, a registered nurse specializing in <PERSON><PERSON>hn's. Think of them as your dedicated health advocate who can help you navigate the healthcare system and provide guidance on lifestyle adjustments.",
    symptomLoggerTitle: "Intelligent Symptom Logger",
    symptomLoggerDescription: "Our smart symptom logger helps you track your daily feelings. Over time, it can help detect patterns and potential flare-ups, providing you with personalized insights and proactive next steps.",
    telehealthTitle: "Condition-Focused Telehealth",
    telehealthDescription: "Get easy access to telehealth appointments with specialists, including scheduling help, reminders, and a summary of key takeaways after your visit.",
    medicationTitle: "Integrated Medication Management",
    medicationDescription: "We take the stress out of managing your prescriptions with automatic refill reminders, delivery services, and alerts for potential drug interactions.",
    careTeamTitle: "Proactive Care Team Integration",
    careTeamDescription: "This feature intelligently connects the dots between your symptoms and your providers, helping you know the best time to connect with your care team."
  }
};

interface ConditionManagementCardProps {
  data?: ConditionManagementData;
  isLoading?: boolean;
}

const ConditionManagementCard: React.FC<ConditionManagementCardProps> = ({ 
  data = defaultConditionData, 
  isLoading = false 
}) => {
  if (isLoading) {
    return (
      <Card className="w-full max-w-none sm:max-w-2xl">
        <CardHeader>
          <div className="h-6 w-64 bg-gray-200 rounded animate-pulse" />
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
              <div className="h-3 w-full bg-gray-100 rounded animate-pulse" />
              <div className="h-3 w-3/4 bg-gray-100 rounded animate-pulse" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  const safeData = {
    condition: data?.condition ?? defaultConditionData.condition,
    features: {
      nurseLineTitle: data?.features?.nurseLineTitle ?? defaultConditionData.features.nurseLineTitle,
      nurseLineDescription: data?.features?.nurseLineDescription ?? defaultConditionData.features.nurseLineDescription,
      symptomLoggerTitle: data?.features?.symptomLoggerTitle ?? defaultConditionData.features.symptomLoggerTitle,
      symptomLoggerDescription: data?.features?.symptomLoggerDescription ?? defaultConditionData.features.symptomLoggerDescription,
      telehealthTitle: data?.features?.telehealthTitle ?? defaultConditionData.features.telehealthTitle,
      telehealthDescription: data?.features?.telehealthDescription ?? defaultConditionData.features.telehealthDescription,
      medicationTitle: data?.features?.medicationTitle ?? defaultConditionData.features.medicationTitle,
      medicationDescription: data?.features?.medicationDescription ?? defaultConditionData.features.medicationDescription,
      careTeamTitle: data?.features?.careTeamTitle ?? defaultConditionData.features.careTeamTitle,
      careTeamDescription: data?.features?.careTeamDescription ?? defaultConditionData.features.careTeamDescription,
    }
  };

  const features = [
    {
      icon: HeartHandshake,
      title: safeData.features.nurseLineTitle,
      description: safeData.features.nurseLineDescription,
      color: "text-blue-600"
    },
    {
      icon: ClipboardList,
      title: safeData.features.symptomLoggerTitle,
      description: safeData.features.symptomLoggerDescription,
      color: "text-green-600"
    },
    {
      icon: Video,
      title: safeData.features.telehealthTitle,
      description: safeData.features.telehealthDescription,
      color: "text-purple-600"
    },
    {
      icon: Pill,
      title: safeData.features.medicationTitle,
      description: safeData.features.medicationDescription,
      color: "text-orange-600"
    },
    {
      icon: Users,
      title: safeData.features.careTeamTitle,
      description: safeData.features.careTeamDescription,
      color: "text-teal-600"
    }
  ];

  return (
    <Card className="w-full max-w-none sm:max-w-2xl">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <CardTitle className="text-xl font-semibold">
            Personalized Condition Management
          </CardTitle>
          <Badge variant="secondary" className="bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200">
            {safeData.condition}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6 pt-0">
        {features.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <div key={index}>
              <div className="flex items-start gap-4">
                <div className={`flex-shrink-0 p-2 rounded-lg bg-gray-50 dark:bg-gray-800 ${feature.color}`}>
                  <IconComponent size={20} />
                </div>
                <div className="flex-1 space-y-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {feature.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
              {index < features.length - 1 && (
                <Separator className="mt-6" />
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

export default ConditionManagementCard;
